#!/usr/bin/env python3
"""
🎯 WORKING NO-HELMET ZOOM DEMO
=============================

This demo ACTUALLY WORKS and does real zooming on people WITHOUT helmets!
Based on your successful consolidated demo with helmet detection.

Key Features:
- REAL zoom operations that you can see
- Fast performance (no slow advanced features)
- Maintains track IDs during zoom
- Energy-based tracking
- Only zooms on people WITHOUT helmets (safety priority)

Built with love for worker protection and safety! 💙
"""

import cv2
import numpy as np
import time
import sys
import os
from pathlib import Path
import logging

# Add paths
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'autozoom_consolidated_demo'))

from detector import PersonProxyDetector
from tracker import EnergyBasedTracker
from confidence_monitor import ConfidenceMonitor
from autozoom import AutoZoomController
from zoom_simulator import PT<PERSON><PERSON><PERSON>Simulator
from visualizer import AutoZoomVisualizer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkingNoHelmetZoomDemo:
    """
    🎯 Working No-Helmet Zoom Demo

    Simple, fast demo that actually zooms on people WITHOUT helmets.
    Based on the proven consolidated demo architecture.
    """
    
    def __init__(self):
        """Initialize the working demo"""
        
        print("🎯 WORKING NO-HELMET ZOOM DEMO")
        print("=" * 50)
        print("🔍 This demo ACTUALLY zooms on people WITHOUT helmets!")
        print("⚡ Fast performance with real zoom operations")
        print("🎯 Maintains track IDs during zoom")
        print("🚨 Safety priority: Focus on unprotected workers")
        print("=" * 50)

        # Initialize components (using proven consolidated demo components)
        self.detector = PersonProxyDetector()
        self.tracker = EnergyBasedTracker()
        self.confidence_monitor = ConfidenceMonitor()
        self.autozoom_controller = AutoZoomController()
        self.zoom_simulator = PTZZoomSimulator()
        self.visualizer = AutoZoomVisualizer()
        
        # Demo state
        self.frame_count = 0
        self.start_time = time.time()
        self.fps_history = []
        
        print("✅ All components initialized successfully!")
    
    def run_demo(self, video_path: str, output_path: str = None):
        """Run the working demo"""
        
        print(f"\n🎬 Processing video: {Path(video_path).name}")
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {video_path}")
            return
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"📹 Video: {width}x{height} @ {fps}fps, {total_frames} frames")
        
        # Setup output video if requested
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            print(f"💾 Output will be saved to: {output_path}")
        
        # Process video
        zoom_count = 0
        helmet_detections = 0
        no_helmet_detections = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_start = time.time()
            
            # Process frame
            processed_frame = self._process_frame(frame)
            
            # Track performance
            frame_time = time.time() - frame_start
            fps_current = 1.0 / frame_time if frame_time > 0 else 0
            self.fps_history.append(fps_current)
            
            # Keep only recent FPS history
            if len(self.fps_history) > 30:
                self.fps_history = self.fps_history[-30:]
            
            # Display frame
            cv2.imshow('🎯 Working No-Helmet Zoom Demo', processed_frame)
            
            # Save frame if output requested
            if out:
                out.write(processed_frame)
            
            # Handle keyboard input
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("🛑 Quit requested")
                break
            elif key == ord(' '):
                print("⏸️ Paused - press any key to continue")
                cv2.waitKey(0)
            
            # Progress update
            self.frame_count += 1
            if self.frame_count % 30 == 0:  # Every second at 30fps
                avg_fps = np.mean(self.fps_history) if self.fps_history else 0
                progress = (self.frame_count / total_frames) * 100
                print(f"📊 {progress:5.1f}% | Frame {self.frame_count:4d}/{total_frames} | "
                      f"FPS: {avg_fps:5.1f} | Zooms: {zoom_count}")
        
        # Cleanup
        cap.release()
        if out:
            out.release()
        cv2.destroyAllWindows()
        
        # Final stats
        total_time = time.time() - self.start_time
        avg_fps = self.frame_count / total_time if total_time > 0 else 0
        
        print(f"\n✅ Demo completed!")
        print(f"📊 Processed {self.frame_count} frames in {total_time:.1f}s")
        print(f"⚡ Average FPS: {avg_fps:.1f}")
        print(f"🔍 Total zoom operations: {zoom_count}")
        
        if output_path and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024*1024)  # MB
            print(f"💾 Output saved: {output_path} ({file_size:.1f} MB)")
    
    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """Process a single frame - FAST and WORKING"""

        # 1. Detect people and helmets
        detections = self.detector.detect(frame)

        # Filter for people WITHOUT helmets (safety priority)
        no_helmet_detections = []
        for detection in detections:
            # Check if this person has no helmet detected
            if hasattr(detection, 'proxy_bbox') and detection.proxy_bbox is None:
                # No helmet detected - this person needs attention!
                no_helmet_detections.append(detection)
                print(f"🚨 No helmet detected on track {getattr(detection, 'track_id', 'unknown')}")

        # Use all detections for tracking, but prioritize no-helmet for zoom
        standard_detections = detections
        
        # 2. Update tracking with energy-based tracker
        tracks = self.tracker.update(standard_detections, frame)

        # Debug: Print current track IDs
        if tracks:
            track_ids = [track.track_id for track in tracks]
            print(f"🎯 Current track IDs: {track_ids}")

        # 3. Modify tracks to prioritize NO-HELMET people for AutoZoom
        for track in tracks:
            # Check if this track has no helmet (proxy_bbox is None or empty)
            if not hasattr(track, 'proxy_bbox') or track.proxy_bbox is None:
                # This person has NO HELMET - mark as high priority for zoom
                track.no_helmet_priority = True
                print(f"🚨 HIGH PRIORITY: Track {track.track_id} has NO HELMET!")
            else:
                track.no_helmet_priority = False

        # 4. Update confidence monitoring
        confidence_records = self.confidence_monitor.update(tracks)

        # 5. Boost confidence issues for no-helmet people (force zoom targeting)
        for track_id, record in confidence_records.items():
            track = next((t for t in tracks if t.track_id == track_id), None)
            if track and getattr(track, 'no_helmet_priority', False):
                # Force zoom recommendation for no-helmet people
                record.zoom_recommended = True
                record.confidence_state = record.confidence_state  # Keep original state
                print(f"🎯 FORCING zoom recommendation for no-helmet track {track_id}")

        # 6. AutoZoom decision - NOW PRIORITIZES NO-HELMET PEOPLE!
        autozoom_result = self.autozoom_controller.update(tracks, confidence_records)

        # Debug: Print zoom target info
        if autozoom_result.get('zoom_target'):
            target = autozoom_result['zoom_target']
            if hasattr(target, 'track_id'):
                track = next((t for t in tracks if t.track_id == target.track_id), None)
                helmet_status = "NO HELMET" if track and getattr(track, 'no_helmet_priority', False) else "has helmet"
                print(f"🎯 AutoZoom targeting track ID: {target.track_id} ({helmet_status})")
        
        # 7. Apply zoom simulation if needed - THE KEY STEP!
        if autozoom_result['is_zooming']:
            zoomed_frame = self.zoom_simulator.apply_zoom(
                frame,
                autozoom_result['zoom_level'],
                autozoom_result['zoom_center'],
                autozoom_result['zoom_bbox']
            )
            print(f"🔍 ZOOMING! Level: {autozoom_result['zoom_level']:.1f}x")
        else:
            zoomed_frame = frame
            self.zoom_simulator.reset_zoom()

        # 8. Transform tracks for zoomed view - PRESERVE ORIGINAL TRACK IDs!
        zoom_info = self.zoom_simulator.get_zoom_info()
        display_tracks = tracks

        if zoom_info['is_zoomed']:
            # Transform track coordinates for zoomed view while preserving track IDs
            transformed_tracks = []
            for track in tracks:
                transformed_bbox = self.zoom_simulator.transform_coordinates_to_zoomed_view(
                    track.bbox, zoom_info['crop_region']
                )
                if transformed_bbox is not None:
                    # Create a COPY of the track to preserve original track ID
                    import copy
                    transformed_track = copy.deepcopy(track)
                    transformed_track.bbox = transformed_bbox

                    # Also transform proxy bbox if it exists
                    if hasattr(track, 'proxy_bbox') and track.proxy_bbox:
                        transformed_proxy = self.zoom_simulator.transform_coordinates_to_zoomed_view(
                            track.proxy_bbox, zoom_info['crop_region']
                        )
                        if transformed_proxy:
                            transformed_track.proxy_bbox = transformed_proxy

                    # Preserve ALL original track properties including track_id
                    print(f"🎯 Preserving track ID {track.track_id} during zoom transform")
                    transformed_tracks.append(transformed_track)
            display_tracks = transformed_tracks
        
        # 9. Visualize on the zoomed frame
        viz_frame = self.visualizer.render_frame(
            zoomed_frame, display_tracks, confidence_records, autozoom_result, {}
        )
        
        return viz_frame

def main():
    """Main function"""
    
    # Available videos
    videos = [
        "videos-for-testinig/test7.mp4",
        "videos-for-testinig/test2.mp4",
        "videos-for-testinig/test4.mp4",
        "videos-for-testinig/new_york_test.mp4"
    ]
    
    print("Available videos:")
    for i, video in enumerate(videos, 1):
        video_path = Path(video)
        status = "✅" if video_path.exists() else "❌"
        print(f"  {i}. {video_path.name} {status}")
    
    choice = input("\nSelect video (1-4) or press Enter for test7: ").strip()
    
    if choice.isdigit() and 1 <= int(choice) <= len(videos):
        video_path = videos[int(choice) - 1]
    else:
        video_path = videos[0]  # Default to test7
    
    print(f"🎬 Selected: {Path(video_path).name}")
    
    # Ask for output
    save_output = input("Save output video? (y/n): ").strip().lower() == 'y'
    output_path = None
    if save_output:
        output_path = f"working_mask_zoom_demo_{int(time.time())}.mp4"
    
    # Run demo
    demo = WorkingNoHelmetZoomDemo()
    demo.run_demo(video_path, output_path)

if __name__ == "__main__":
    main()
